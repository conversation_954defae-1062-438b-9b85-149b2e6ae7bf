from django.utils import timezone
from rest_framework.views import APIView
from rest_framework import status
from rest_framework.response import Response

from Authentication.customer_verifier import CustomJWTAuthentication
from Authentication.models import *
from django.db.models import Q
from Project.models import *
from helpers.decode_id import decode_token
from helpers.pagination import CustomPagination
from helpers.str_to_bool import convert_to_bool


class GetAllProfile(APIView):
    authentication_classes=[CustomJWTAuthentication]

    def get(self,request):
        try:
            token = request.headers.get('Authorization')
            user_id = decode_token(token)
            block_profiles = BlockProfile.objects.filter(user=user_id)
            block_profile_ids = [profile.blocked_profile.id for profile in block_profiles]
            report_profiles = ReportProfile.objects.filter(user=user_id)    
            report_profile_ids = [profile.profile.id for profile in report_profiles]
            user_profiles = UserProfile.objects.filter(is_active=True).exclude(id__in=block_profile_ids).exclude(id__in=report_profile_ids)
            response_data = []
            for profile in user_profiles:
                append_obj = {
                    'id':profile.pk,
                    'name':profile.full_name,
                    'profile_image':profile.profile_picture.url if profile.profile_picture else '',
                    'prefered_smoking':profile.prefered_smoking,
                    'cleaniness':profile.cleaniness,
                    'prefered_lease_period':profile.prefered_lease_period,
                    'pets':profile.is_having_pet,
                    'class_standing':profile.class_standing
                }
                response_data.append(append_obj)
            return Response({'status':True,'message':'Data Found Successfully','data':response_data},status=status.HTTP_200_OK)
        except Exception as e :
            return Response({'status':False,'message':f'Error -{e}'},status=status.HTTP_400_BAD_REQUEST)

class LikeProfileView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self,request):
        try:
            token = request.headers.get('Authorization')
            user_id = decode_token(token)
            like_profile_id = request.data.get('like_profile_id')

            try:
                liked_profile = LikeProfile.objects.get(user_id=user_id,liked_profile_id=like_profile_id)
                return Response({'status':False,'message':'Profile Already Liked'},status=status.HTTP_400_BAD_REQUEST)
            except LikeProfile.DoesNotExist:
                LikeProfile.objects.create(user_id=user_id,liked_profile_id=like_profile_id)
                return Response({'status':True,'message':'Profile Liked Successfully'},status=status.HTTP_200_OK)
        except Exception as e :
            return Response({'status':False,'message':f'Error -{e}'},status=status.HTTP_400_BAD_REQUEST)

class DislikeProfileView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self,request):
        try:
            token = request.headers.get('Authorization')
            user_id = decode_token(token)
            like_profile_id = request.data.get('dislike_profile_id')

            try:
                liked_profile = DislikeProfile.objects.get(user_id=user_id,disliked_profile_id=like_profile_id)
                return Response({'status':False,'message':'Profile Already Disliked'},status=status.HTTP_400_BAD_REQUEST)
            except DislikeProfile.DoesNotExist:
                DislikeProfile.objects.create(user_id=user_id,disliked_profile_id=like_profile_id)
                return Response({'status':True,'message':'Profile Disliked Successfully'},status=status.HTTP_200_OK)
        except Exception as e :
            return Response({'status':False,'message':f'Error -{e}'},status=status.HTTP_400_BAD_REQUEST)
        

class ProfileLikeInwards(APIView):
    authentication_classes=[CustomJWTAuthentication]

    def get(self,request):
        try:
            token = request.headers.get('Authorization')
            user_id = decode_token(token)

            try:
                liked_profiles = LikeProfile.objects.filter(liked_profile__user_id=user_id)
                response_data = []
                for profile in liked_profiles:
                    user_profile = UserProfile.objects.get(user_id=profile.user.pk)
                    append_obj = {
                        'id':user_profile.pk,
                        'name':user_profile.full_name,
                        'year':user_profile.class_standing,
                        'status':profile.is_accepted
                    }
                    response_data.append(append_obj)
                
                return Response({'status':True,'message':'Data Found Successfully','data':response_data},status=status.HTTP_200_OK)
            except LikeProfile.DoesNotExist:
                return Response({'status':True,'message':'Data Not Found','data':[]},status=status.HTTP_200_OK)
        except Exception as e :
            return Response({'status':False,'message':f'Error -{e}'},status=status.HTTP_400_BAD_REQUEST)




# class AcceptLikeProfileView(APIView):
#     authentication_classes = [CustomJWTAuthentication]

#     def post(self, request):
#         try:
#             token = request.headers.get('Authorization')
#             user_id = decode_token(token)

#             like_profile_id = request.data.get('like_profile_id')
#             is_accepted = convert_to_bool(request.data.get('is_accepted'))


#             try:
#                 liked_profile = LikeProfile.objects.get(user_id=user_id, liked_profile_id=like_profile_id)
#                 liked_profile.is_accepted = is_accepted
#                 liked_profile.save()

#                 msg = 'Profile Accepted Successfully' if is_accepted else 'Profile Rejected Successfully'
#                 return Response({'status': True, 'message': msg}, status=status.HTTP_200_OK)
#             except LikeProfile.DoesNotExist:
#                 return Response({'status': False, 'message': 'Profile Not Found'}, status=status.HTTP_400_BAD_REQUEST)

#         except Exception as e:
#             return Response({'status': False, 'message': f'Error - {e}'}, status=status.HTTP_400_BAD_REQUEST)



class ProfileLikeOutwards(APIView):
    authentication_classes=[CustomJWTAuthentication]

    def get(self,request):
        try:
            token = request.headers.get('Authorization')
            user_id = decode_token(token)

            try:
                liked_profiles = LikeProfile.objects.filter(user_id=user_id)
                response_data = []
                for profile in liked_profiles:
                    append_obj = {
                        'id':profile.pk,
                        'name':profile.liked_profile.full_name,
                        'year':profile.liked_profile.class_standing,
                        'status':profile.is_accepted
                    }
                    response_data.append(append_obj)
                
                return Response({'status':True,'message':'Data Found Successfully','data':response_data},status=status.HTTP_200_OK)
            except LikeProfile.DoesNotExist:
                return Response({'status':True,'message':'Data Not Found','data':[]},status=status.HTTP_200_OK)
        except Exception as e :
            return Response({'status':False,'message':f'Error -{e}'},status=status.HTTP_400_BAD_REQUEST)
        
class DetailedProfileView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def get(self,request):
        try:
            token = request.headers.get('Authorization')
            user_id = decode_token(token)  
            profile_id = request.query_params.get('profile_id')
            try:
                profile_data = UserProfile.objects.get(user_id=profile_id)
                response_data = {
                    'id':profile_data.pk,
                    'profile_image':profile_data.profile_picture.url if profile_data.profile_picture else '',
                    'name':profile_data.full_name,
                    'gender':profile_data.gender,
                    'dob':profile_data.dob,
                    'cleaniness':profile_data.cleaniness,
                    'lease_period':profile_data.prefered_lease_period,
                    'prefered_gender':profile_data.prefered_gender,
                    'prefered_locations':profile_data.prefered_locations,
                    'personality_type_description':profile_data.personality_type_description,
                    'habits_lifestyle':profile_data.habits_lifestyle,
                    'living_style':profile_data.living_style,
                    'interests_hobbies':profile_data.interests_hobbies,
                    'contact_number':profile_data.contact_number, 
                    'is_verified':profile_data.is_verified,     
                    'is_active':profile_data.is_active,              
                    'year':profile_data.class_standing,
                    'about':profile_data.about,
                    'smoker':profile_data.prefered_smoking,
                    'pet':profile_data.is_having_pet,
                    'additional_profiles_images':[i.picture.url for i in UserProfilePictures.objects.filter(user_profile_id=profile_data.pk) if i.picture]
                }
                return Response({'status':True,'message':'Data Found Successfully','data':response_data},status=status.HTTP_200_OK)
            except UserProfile.DoesNotExist:
                return Response({'status':True,'message':'Data Not Found','data':{}},status=status.HTTP_200_OK)
        except Exception as e :
            return Response({'status':False,'message':f'Error -{e}'},status=status.HTTP_400_BAD_REQUEST)

class MessageListView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = CustomPagination
    
    def get(self, request):
        auth_token = request.headers.get('Authorization')
        logged_in_user_id = decode_token(auth_token)
        chat_user_id = request.query_params.get('chat_user_id')

        if not chat_user_id:
            return Response({'status': False, 'message': 'chat_user_id is required.'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            messages = ChatMessage.objects.filter(
                (Q(from_user_id=logged_in_user_id) & Q(to_user_id=chat_user_id)) |
                (Q(from_user_id=chat_user_id) & Q(to_user_id=logged_in_user_id))
            ).order_by('-created_at', '-id')

            message_list = []

            for msg in messages:
                created_at_local = timezone.localtime(msg.created_at).isoformat()
                message_data = {
                    'id': msg.id,
                    'type': msg.type,
                    'is_read': msg.is_read,
                    'created_at': created_at_local,
                    'sent_by': msg.from_user.pk
                }

                if msg.type in ['text', 'message']:
                    message_data['message'] = msg.messages

                elif msg.type in ['image', 'voice', 'custom']:
                    message_data['message'] = f'{msg.file}'
                    message_data['file'] = f'{msg.file}'
                message_list.append(message_data)

            paginator = self.pagination_class()
            paginated_message_list = paginator.paginate_queryset(message_list, request)
            return paginator.get_paginated_response({
                'status': True,
                'message': 'Data Found Successfully',
                'data': paginated_message_list
            })

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)